using System;

namespace USDP2
{
    /// <summary>
    /// Simple test class to verify Blowfish compilation.
    /// </summary>
    public static class BlowfishTest
    {
        /// <summary>
        /// Tests basic Blowfish functionality.
        /// </summary>
        public static void TestBasicFunctionality()
        {
            try
            {
                // Test basic instantiation
                using var bf = new BlowFish("0123456789ABCDEF");
                
                // Test encryption/decryption
                var plaintext = "Hello, World!";
                var encrypted = bf.Encrypt_CBC(plaintext);
                var decrypted = bf.Decrypt_CBC(encrypted);
                
                Console.WriteLine($"Original: {plaintext}");
                Console.WriteLine($"Encrypted: {encrypted}");
                Console.WriteLine($"Decrypted: {decrypted}");
                
                // Test security recommendations
                var recommendations = BlowFish.GetSecurityRecommendations();
                Console.WriteLine(recommendations);
                
                // Test performance metrics
                var metrics = bf.GetPerformanceMetrics();
                Console.WriteLine(metrics);
                
                Console.WriteLine("✅ Blowfish test completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Blowfish test failed: {ex.Message}");
                throw;
            }
        }
    }
}
